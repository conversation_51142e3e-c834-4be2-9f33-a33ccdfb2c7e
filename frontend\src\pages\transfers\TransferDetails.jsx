import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  Chip,
  Grid,
  Card,
  CardContent,
  Divider,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Person as PersonIcon,
  SwapHoriz as TransferIcon,
  Description as DocumentIcon,
  Badge as BadgeIcon,
  Visibility as ViewIcon,
  Print as PrintIcon,
  Check as AcceptIcon,
  Close as RejectIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import TransferPrintView from '../../components/transfers/TransferPrintView';

const TransferDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [transfer, setTransfer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Review dialog state
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [reviewAction, setReviewAction] = useState('accept');
  const [reviewNotes, setReviewNotes] = useState('');
  const [submitting, setSubmitting] = useState(false);

  // Print dialog state
  const [printDialogOpen, setPrintDialogOpen] = useState(false);

  useEffect(() => {
    fetchTransferDetails();
  }, [id]);

  const fetchTransferDetails = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await axios.get(`/api/tenants/transfers/${id}/`);
      setTransfer(response.data);
      console.log('✅ Transfer details:', response.data);
    } catch (error) {
      console.error('❌ Error fetching transfer details:', error);
      setError('Failed to load transfer details');
    } finally {
      setLoading(false);
    }
  };

  const handleReview = (action) => {
    setReviewAction(action);
    setReviewNotes('');
    setReviewDialogOpen(true);
  };

  const submitReview = async () => {
    if (!transfer) return;

    try {
      setSubmitting(true);

      const reviewData = {
        action: reviewAction,
        review_notes: reviewNotes.trim()
      };

      await axios.post(`/api/tenants/transfers/${transfer.id}/review/`, reviewData);
      
      setReviewDialogOpen(false);
      setReviewNotes('');
      
      // Refresh transfer details
      fetchTransferDetails();
      
      alert(`Transfer request ${reviewAction === 'accept' ? 'accepted' : 'rejected'} successfully!`);
    } catch (error) {
      console.error('Error submitting review:', error);
      alert('Failed to submit review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleComplete = async () => {
    if (!window.confirm(`Are you sure you want to complete the transfer for ${transfer.citizen_name}?`)) {
      return;
    }

    try {
      await axios.post(`/api/tenants/transfers/${transfer.id}/complete/`);
      
      // Refresh transfer details
      fetchTransferDetails();
      
      alert('Transfer completed successfully!');
    } catch (error) {
      console.error('Error completing transfer:', error);
      alert('Failed to complete transfer. Please try again.');
    }
  };

  const handlePrint = () => {
    setPrintDialogOpen(true);
  };

  const handlePrintConfirm = () => {
    setPrintDialogOpen(false);
    // Small delay to allow dialog to close before printing
    setTimeout(() => {
      window.print();
    }, 100);
  };

  const openDocument = (documentUrl) => {
    if (documentUrl) {
      window.open(documentUrl, '_blank');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'accepted': return 'info';
      case 'completed': return 'success';
      case 'rejected': return 'error';
      case 'cancelled': return 'default';
      default: return 'default';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !transfer) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error || 'Transfer not found'}</Alert>
        <Button
          startIcon={<BackIcon />}
          onClick={() => navigate('/transfers')}
          sx={{ mt: 2 }}
        >
          Back to Transfers
        </Button>
      </Box>
    );
  }

  // Debug permission checks
  console.log('🔍 Permission Debug:', {
    transfer_status: transfer.status,
    transfer_destination_kebele: transfer.destination_kebele,
    transfer_source_kebele: transfer.source_kebele,
    user_tenant_id: user?.tenant?.id,
    user_role: user?.role,
    user_tenant: user?.tenant
  });

  const canReview = transfer.status === 'pending' &&
                   transfer.destination_kebele === user?.tenant?.id &&
                   (user?.role === 'kebele_leader' || user?.role === 'clerk');

  // Note: In the new workflow, transfers are automatically completed when accepted
  // No separate "Complete" step is needed
  const canComplete = false; // Disabled - transfers auto-complete on acceptance

  console.log('🔍 Permission Results:', {
    canReview,
    canComplete: false, // Always false in new workflow
    transfer_status: transfer.status,
    note: 'Transfers auto-complete when accepted'
  });

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Button
            startIcon={<BackIcon />}
            onClick={() => navigate('/transfers')}
            variant="outlined"
          >
            Back
          </Button>
          <Typography variant="h4" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <TransferIcon color="primary" sx={{ fontSize: 40 }} />
            Transfer Details
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<PrintIcon />}
            onClick={handlePrint}
          >
            Print
          </Button>
          
          {canReview && (
            <>
              <Button
                variant="contained"
                color="success"
                startIcon={<AcceptIcon />}
                onClick={() => handleReview('accept')}
              >
                Accept
              </Button>
              <Button
                variant="contained"
                color="error"
                startIcon={<RejectIcon />}
                onClick={() => handleReview('reject')}
              >
                Reject
              </Button>
            </>
          )}
          
          {canComplete && (
            <Button
              variant="contained"
              color="primary"
              onClick={handleComplete}
            >
              Complete Transfer
            </Button>
          )}
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Transfer Information */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TransferIcon color="primary" />
              Transfer Information
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Transfer ID:</Typography>
                <Typography variant="body1" fontWeight="medium">{transfer.transfer_id}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Status:</Typography>
                <Chip
                  label={transfer.status_display}
                  color={getStatusColor(transfer.status)}
                  size="small"
                />
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">From Kebele:</Typography>
                <Typography variant="body1" fontWeight="medium">
                  {transfer.source_kebele_info?.name || 'Unknown'}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">To Kebele:</Typography>
                <Typography variant="body1" fontWeight="medium">
                  {transfer.destination_kebele_info?.name || 'Unknown'}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Transfer Reason:</Typography>
                <Typography variant="body1" fontWeight="medium">
                  {transfer.transfer_reason_display}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Created Date:</Typography>
                <Typography variant="body1" fontWeight="medium">
                  {formatDate(transfer.created_at)}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">Reason Description:</Typography>
                <Typography variant="body1">
                  {transfer.reason_description || 'No description provided'}
                </Typography>
              </Grid>
            </Grid>
          </Paper>

          {/* Citizen Information */}
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <PersonIcon color="primary" />
              Citizen Information
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Full Name:</Typography>
                <Typography variant="body1" fontWeight="medium">{transfer.citizen_name}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Digital ID:</Typography>
                <Typography variant="body1" fontWeight="medium">
                  {transfer.citizen_digital_id || transfer.citizen_id}
                </Typography>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Documents and Actions */}
        <Grid item xs={12} md={4}>
          {/* Documents */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <DocumentIcon color="primary" />
              Required Documents
            </Typography>
            
            {/* Application Letter */}
            <Card sx={{ mb: 2, border: '1px solid', borderColor: 'grey.300' }}>
              <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <DocumentIcon color={transfer.application_letter ? 'success' : 'disabled'} />
                  <Typography variant="subtitle2">Application Letter</Typography>
                  {transfer.application_letter && (
                    <Chip label="Uploaded" color="success" size="small" />
                  )}
                </Box>
                
                {transfer.application_letter ? (
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="View Document">
                      <IconButton
                        size="small"
                        onClick={() => openDocument(transfer.application_letter_url)}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Download">
                      <IconButton
                        size="small"
                        onClick={() => window.open(transfer.application_letter_url, '_blank')}
                      >
                        <DownloadIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Not uploaded
                  </Typography>
                )}
              </CardContent>
            </Card>

            {/* Kebele ID Card */}
            <Card sx={{ border: '1px solid', borderColor: 'grey.300' }}>
              <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <BadgeIcon color={transfer.current_kebele_id ? 'success' : 'disabled'} />
                  <Typography variant="subtitle2">Current Kebele ID</Typography>
                  {transfer.current_kebele_id && (
                    <Chip label="Uploaded" color="success" size="small" />
                  )}
                </Box>
                
                {transfer.current_kebele_id ? (
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="View Document">
                      <IconButton
                        size="small"
                        onClick={() => openDocument(transfer.current_kebele_id_url)}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Download">
                      <IconButton
                        size="small"
                        onClick={() => window.open(transfer.current_kebele_id_url, '_blank')}
                      >
                        <DownloadIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Not uploaded
                  </Typography>
                )}
              </CardContent>
            </Card>

            {transfer.documents_uploaded_at && (
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                Documents uploaded: {formatDate(transfer.documents_uploaded_at)}
              </Typography>
            )}
          </Paper>

          {/* Review Information */}
          {(transfer.reviewed_at || transfer.review_notes) && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Review Information
              </Typography>

              {transfer.reviewed_by_username && (
                <Box sx={{ mb: 1 }}>
                  <Typography variant="body2" color="text.secondary">Reviewed by:</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {transfer.reviewed_by_username}
                  </Typography>
                </Box>
              )}

              {transfer.reviewed_at && (
                <Box sx={{ mb: 1 }}>
                  <Typography variant="body2" color="text.secondary">Review Date:</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {formatDate(transfer.reviewed_at)}
                  </Typography>
                </Box>
              )}

              {transfer.review_notes && (
                <Box>
                  <Typography variant="body2" color="text.secondary">Review Notes:</Typography>
                  <Typography variant="body1">
                    {transfer.review_notes}
                  </Typography>
                </Box>
              )}
            </Paper>
          )}

          {/* Completion Information */}
          {transfer.status === 'completed' && (
            <Paper sx={{ p: 3, bgcolor: 'success.50', border: '1px solid', borderColor: 'success.200' }}>
              <Typography variant="h6" gutterBottom sx={{ color: 'success.main' }}>
                ✅ Transfer Completed
              </Typography>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                This transfer has been completed successfully. The citizen has been moved to the destination kebele with a new digital ID.
              </Typography>

              {transfer.completed_at && (
                <Box sx={{ mb: 1 }}>
                  <Typography variant="body2" color="text.secondary">Completed Date:</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {formatDate(transfer.completed_at)}
                  </Typography>
                </Box>
              )}

              {transfer.new_citizen_id && (
                <Box>
                  <Typography variant="body2" color="text.secondary">New Citizen ID:</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {transfer.new_citizen_id}
                  </Typography>
                </Box>
              )}
            </Paper>
          )}

          {/* Auto-Completion Notice for Accepted Transfers */}
          {transfer.status === 'accepted' && (
            <Paper sx={{ p: 3, bgcolor: 'info.50', border: '1px solid', borderColor: 'info.200' }}>
              <Typography variant="h6" gutterBottom sx={{ color: 'info.main' }}>
                🔄 Transfer In Progress
              </Typography>

              <Typography variant="body2" color="text.secondary">
                This transfer has been accepted and the citizen transfer process is being completed automatically.
                The citizen will be moved to the destination kebele with all their related data.
              </Typography>
            </Paper>
          )}
        </Grid>
      </Grid>

      {/* Review Dialog */}
      <Dialog open={reviewDialogOpen} onClose={() => setReviewDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {reviewAction === 'accept' ? 'Accept' : 'Reject'} Transfer Request
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body1" gutterBottom>
              <strong>Citizen:</strong> {transfer.citizen_name}
            </Typography>
            <Typography variant="body1" gutterBottom>
              <strong>From:</strong> {transfer.source_kebele_info?.name}
            </Typography>
            <Typography variant="body1" gutterBottom>
              <strong>Reason:</strong> {transfer.transfer_reason_display}
            </Typography>
          </Box>
          
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Review Notes"
            value={reviewNotes}
            onChange={(e) => setReviewNotes(e.target.value)}
            placeholder={`Please provide notes for ${reviewAction === 'accept' ? 'accepting' : 'rejecting'} this transfer request...`}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReviewDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={submitReview}
            variant="contained"
            color={reviewAction === 'accept' ? 'success' : 'error'}
            disabled={submitting}
          >
            {submitting ? 'Processing...' : (reviewAction === 'accept' ? 'Accept' : 'Reject')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Print Dialog */}
      <Dialog open={printDialogOpen} onClose={() => setPrintDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Print Transfer Document</DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body1" gutterBottom>
              This will print the complete transfer request document including all details and signatures.
            </Typography>
          </Box>

          {/* Print Preview */}
          <Box sx={{
            maxHeight: '400px',
            overflow: 'auto',
            border: '1px solid #ddd',
            borderRadius: 1,
            transform: 'scale(0.7)',
            transformOrigin: 'top left',
            width: '142.86%', // Compensate for scale
            height: '142.86%'
          }}>
            <TransferPrintView transfer={transfer} />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPrintDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handlePrintConfirm}
            variant="contained"
            startIcon={<PrintIcon />}
          >
            Print Document
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TransferDetails;
