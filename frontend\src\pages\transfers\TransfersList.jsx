import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  Chip,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Card,
  CardContent,
  Grid
} from '@mui/material';
import {
  SwapHoriz as TransferIcon,
  Visibility as ViewIcon,
  Check as AcceptIcon,
  Close as RejectIcon,
  PlayArrow as CompleteIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
  Assessment as StatsIcon
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';

const TransfersList = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [transfers, setTransfers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [stats, setStats] = useState(null);
  
  // Review dialog state
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [selectedTransfer, setSelectedTransfer] = useState(null);
  const [reviewAction, setReviewAction] = useState('accept');
  const [reviewNotes, setReviewNotes] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const tabs = [
    { label: 'All Transfers', value: 'all' },
    { label: 'Outgoing', value: 'outgoing' },
    { label: 'Incoming', value: 'incoming' },
    { label: 'Pending Review', value: 'pending' },
    { label: 'Statistics', value: 'stats' }
  ];

  useEffect(() => {
    fetchTransfers();
    fetchStats();
  }, [activeTab]);

  const fetchTransfers = async () => {
    try {
      setLoading(true);
      setError('');

      let params = {};
      
      // Set direction filter based on active tab
      if (tabs[activeTab].value === 'outgoing') {
        params.direction = 'outgoing';
      } else if (tabs[activeTab].value === 'incoming') {
        params.direction = 'incoming';
      } else if (tabs[activeTab].value === 'pending') {
        params.status = 'pending';
        params.direction = 'incoming'; // Only show incoming pending requests for review
      }

      const response = await axios.get('/api/tenants/transfers/', { params });
      const transfersData = response.data.results || response.data || [];
      setTransfers(transfersData);

      console.log('✅ Transfers fetched:', response.data);
      console.log('🔍 User object:', user);
      console.log('🔍 User tenant:', user?.tenant);
      console.log('🔍 Active tab:', tabs[activeTab].value);
      console.log('🔍 Transfers count:', transfersData.length);
      if (transfersData.length > 0) {
        console.log('🔍 First transfer:', transfersData[0]);
      }
    } catch (error) {
      console.error('❌ Error fetching transfers:', error);
      setError('Failed to load transfers');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/tenants/transfers/stats/');
      setStats(response.data);
    } catch (error) {
      console.error('Error fetching transfer stats:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'accepted': return 'info';
      case 'completed': return 'success';
      case 'rejected': return 'error';
      case 'cancelled': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'accepted': return '✅';
      case 'completed': return '🎉';
      case 'rejected': return '❌';
      case 'cancelled': return '🚫';
      default: return '❓';
    }
  };

  const handleReview = (transfer, action) => {
    setSelectedTransfer(transfer);
    setReviewAction(action);
    setReviewNotes('');
    setReviewDialogOpen(true);
  };

  const submitReview = async () => {
    if (!selectedTransfer) return;

    try {
      setSubmitting(true);

      const reviewData = {
        action: reviewAction,
        review_notes: reviewNotes.trim()
      };

      await axios.post(`/api/tenants/transfers/${selectedTransfer.id}/review/`, reviewData);
      
      setReviewDialogOpen(false);
      setSelectedTransfer(null);
      setReviewNotes('');
      
      // Refresh transfers list
      fetchTransfers();
      fetchStats();
      
      alert(`Transfer request ${reviewAction === 'accept' ? 'accepted' : 'rejected'} successfully!`);
    } catch (error) {
      console.error('Error submitting review:', error);
      alert('Failed to submit review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleComplete = async (transfer) => {
    if (!window.confirm(`Are you sure you want to complete the transfer for ${transfer.citizen_name}? This will move the citizen to ${transfer.destination_kebele_info?.name}.`)) {
      return;
    }

    try {
      await axios.post(`/api/tenants/transfers/${transfer.id}/complete/`);
      
      // Refresh transfers list
      fetchTransfers();
      fetchStats();
      
      alert('Transfer completed successfully!');
    } catch (error) {
      console.error('Error completing transfer:', error);
      alert('Failed to complete transfer. Please try again.');
    }
  };

  const handleCancel = async (transfer) => {
    if (!window.confirm(`Are you sure you want to cancel the transfer request for ${transfer.citizen_name}?`)) {
      return;
    }

    try {
      await axios.post(`/api/tenants/transfers/${transfer.id}/cancel/`);
      
      // Refresh transfers list
      fetchTransfers();
      fetchStats();
      
      alert('Transfer request cancelled successfully!');
    } catch (error) {
      console.error('Error cancelling transfer:', error);
      alert('Failed to cancel transfer. Please try again.');
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading && transfers.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <TransferIcon color="primary" sx={{ fontSize: 40 }} />
          Citizen Transfers
        </Typography>
        
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={() => {
            fetchTransfers();
            fetchStats();
          }}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          {tabs.map((tab, index) => (
            <Tab key={index} label={tab.label} />
          ))}
        </Tabs>
      </Paper>

      {/* Statistics Tab */}
      {activeTab === 4 && stats && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <StatsIcon color="primary" />
                  Transfer Overview
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography>Total Requests:</Typography>
                    <Chip label={stats.total_requests} color="primary" size="small" />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography>Pending:</Typography>
                    <Chip label={stats.pending_requests} color="warning" size="small" />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography>Completed:</Typography>
                    <Chip label={stats.completed_transfers} color="success" size="small" />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography>Rejected:</Typography>
                    <Chip label={stats.rejected_requests} color="error" size="small" />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Kebele Transfer Balance
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography>Outgoing:</Typography>
                    <Chip label={stats.outgoing_completed} color="warning" size="small" />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography>Incoming:</Typography>
                    <Chip label={stats.incoming_completed} color="info" size="small" />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography>Net Transfer:</Typography>
                    <Chip 
                      label={stats.net_transfer > 0 ? `+${stats.net_transfer}` : stats.net_transfer} 
                      color={stats.net_transfer > 0 ? 'success' : stats.net_transfer < 0 ? 'error' : 'default'} 
                      size="small" 
                    />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Transfers Table */}
      {activeTab !== 4 && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Transfer ID</TableCell>
                <TableCell>Citizen</TableCell>
                <TableCell>From</TableCell>
                <TableCell>To</TableCell>
                <TableCell>Reason</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {transfers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} align="center">
                    <Typography variant="body1" color="text.secondary" sx={{ py: 4 }}>
                      No transfer requests found
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                transfers.map((transfer) => (
                  <TableRow key={transfer.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {transfer.transfer_id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {transfer.citizen_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ID: {transfer.citizen_digital_id || transfer.citizen_id}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {transfer.source_kebele_info?.name || 'Unknown'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {transfer.destination_kebele_info?.name || 'Unknown'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {transfer.transfer_reason_display}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={`${getStatusIcon(transfer.status)} ${transfer.status_display}`}
                        color={getStatusColor(transfer.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDate(transfer.created_at)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        {/* View Details button - always available */}
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => navigate(`/transfers/${transfer.id}`)}
                          title="View details"
                        >
                          <ViewIcon />
                        </IconButton>

                        {/* Review actions for incoming pending requests */}
                        {(() => {
                          // More robust permission checking with multiple comparison methods
                          const statusCheck = transfer.status === 'pending';
                          const roleCheck = user?.role === 'kebele_leader';

                          // Try multiple ways to compare destination kebele
                          const destinationCheck1 = transfer.destination_kebele === user?.tenant?.id;
                          const destinationCheck2 = Number(transfer.destination_kebele) === Number(user?.tenant?.id);
                          const destinationCheck3 = String(transfer.destination_kebele) === String(user?.tenant?.id);

                          const canReview = statusCheck && roleCheck && (destinationCheck1 || destinationCheck2 || destinationCheck3);

                          console.log('🔍 Transfer Review Debug:', {
                            transfer_id: transfer.transfer_id,
                            statusCheck: statusCheck,
                            roleCheck: roleCheck,
                            destinationCheck1: destinationCheck1,
                            destinationCheck2: destinationCheck2,
                            destinationCheck3: destinationCheck3,
                            canReview: canReview,
                            transfer_destination_kebele: transfer.destination_kebele,
                            transfer_destination_kebele_type: typeof transfer.destination_kebele,
                            user_tenant_id: user?.tenant?.id,
                            user_tenant_id_type: typeof user?.tenant?.id,
                            user_role: user?.role
                          });

                          return canReview;
                        })() && (
                          <>
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => handleReview(transfer, 'accept')}
                              title="Accept transfer"
                            >
                              <AcceptIcon />
                            </IconButton>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleReview(transfer, 'reject')}
                              title="Reject transfer"
                            >
                              <RejectIcon />
                            </IconButton>
                          </>
                        )}

                        {/* Complete action for accepted outgoing requests */}
                        {transfer.status === 'accepted' &&
                         transfer.source_kebele === user?.tenant?.id &&
                         user?.role === 'kebele_leader' && (
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleComplete(transfer)}
                            title="Complete transfer"
                          >
                            <CompleteIcon />
                          </IconButton>
                        )}

                        {/* Cancel action for pending outgoing requests */}
                        {transfer.status === 'pending' &&
                         transfer.source_kebele === user?.tenant?.id &&
                         user?.role === 'kebele_leader' && (
                          <IconButton
                            size="small"
                            color="warning"
                            onClick={() => handleCancel(transfer)}
                            title="Cancel transfer"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Review Dialog */}
      <Dialog open={reviewDialogOpen} onClose={() => setReviewDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {reviewAction === 'accept' ? 'Accept' : 'Reject'} Transfer Request
        </DialogTitle>
        <DialogContent>
          {selectedTransfer && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body1" gutterBottom>
                <strong>Citizen:</strong> {selectedTransfer.citizen_name}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>From:</strong> {selectedTransfer.source_kebele_info?.name}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Reason:</strong> {selectedTransfer.transfer_reason_display}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {selectedTransfer.reason_description}
              </Typography>
            </Box>
          )}
          
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Review Notes"
            value={reviewNotes}
            onChange={(e) => setReviewNotes(e.target.value)}
            placeholder={`Please provide notes for ${reviewAction === 'accept' ? 'accepting' : 'rejecting'} this transfer request...`}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReviewDialogOpen(false)} disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={submitReview}
            variant="contained"
            color={reviewAction === 'accept' ? 'success' : 'error'}
            disabled={submitting}
          >
            {submitting ? 'Submitting...' : (reviewAction === 'accept' ? 'Accept' : 'Reject')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TransfersList;
