"""
Fingerprint processing utilities for quality validation, 
duplicate detection, and template management.
"""

import json
import base64
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
from django.db import connection
from django_tenants.utils import schema_context, get_tenant_model

logger = logging.getLogger(__name__)


class FingerprintProcessor:
    """
    Handles fingerprint template processing, validation, and duplicate detection.
    """
    
    def __init__(self):
        self.quality_threshold = 75
        self.match_threshold = 0.8
        self.min_minutiae = 30
    
    def validate_template_quality(self, template_data: str) -> Dict:
        """
        Validate the quality of a fingerprint template.
        
        Args:
            template_data (str): Base64 encoded fingerprint template
            
        Returns:
            Dict: Quality validation result
        """
        try:
            # Decode and parse template
            decoded_data = base64.b64decode(template_data)
            template = json.loads(decoded_data.decode())
            
            quality_score = template.get('quality_metrics', {}).get('clarity', 0)
            minutiae_count = len(template.get('minutiae', []))
            
            # Validate quality criteria
            is_valid = True
            issues = []
            
            if quality_score < self.quality_threshold:
                is_valid = False
                issues.append(f"Quality score {quality_score}% below threshold {self.quality_threshold}%")
            
            if minutiae_count < self.min_minutiae:
                is_valid = False
                issues.append(f"Minutiae count {minutiae_count} below minimum {self.min_minutiae}")
            
            return {
                'is_valid': is_valid,
                'quality_score': quality_score,
                'minutiae_count': minutiae_count,
                'issues': issues,
                'validation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error validating template quality: {e}")
            return {
                'is_valid': False,
                'quality_score': 0,
                'minutiae_count': 0,
                'issues': [f"Template validation error: {str(e)}"],
                'validation_time': datetime.now().isoformat()
            }
    
    def check_duplicates(self, fingerprint_data: Dict) -> Dict:
        """
        Check for duplicate fingerprints across all tenants.
        
        Args:
            fingerprint_data (Dict): Dictionary containing fingerprint templates
            
        Returns:
            Dict: Duplicate check results
        """
        try:
            matches = []
            left_thumb = fingerprint_data.get('left_thumb_fingerprint')
            right_thumb = fingerprint_data.get('right_thumb_fingerprint')
            
            if not left_thumb and not right_thumb:
                return {
                    'has_duplicates': False,
                    'matches': [],
                    'check_time': datetime.now().isoformat()
                }
            
            # Get all kebele tenants
            Tenant = get_tenant_model()
            tenants = Tenant.objects.filter(type='kebele')
            
            for tenant in tenants:
                try:
                    with schema_context(tenant.schema_name):
                        tenant_matches = self._check_tenant_duplicates(
                            left_thumb, right_thumb, tenant
                        )
                        matches.extend(tenant_matches)
                except Exception as e:
                    logger.error(f"Error checking duplicates in tenant {tenant.name}: {e}")
                    continue
            
            return {
                'has_duplicates': len(matches) > 0,
                'matches': matches,
                'total_matches': len(matches),
                'check_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error checking duplicates: {e}")
            return {
                'has_duplicates': False,
                'matches': [],
                'error': str(e),
                'check_time': datetime.now().isoformat()
            }
    
    def _check_tenant_duplicates(self, left_thumb: str, right_thumb: str, tenant) -> List[Dict]:
        """
        Check for duplicates within a specific tenant.
        
        Args:
            left_thumb (str): Left thumb template
            right_thumb (str): Right thumb template
            tenant: Tenant object
            
        Returns:
            List[Dict]: List of matches found
        """
        matches = []
        
        try:
            # Import here to avoid circular imports
            from tenants.models.citizen import Biometric
            
            # Get all biometric records in this tenant
            biometrics = Biometric.objects.select_related('citizen').all()
            
            for biometric in biometrics:
                match_score = 0.0
                match_details = []
                
                # Check left thumb match
                if left_thumb and biometric.left_thumb_fingerprint:
                    left_match, left_confidence = self._compare_templates(
                        left_thumb, biometric.left_thumb_fingerprint
                    )
                    if left_match:
                        match_score += 0.5
                        match_details.append(f"Left thumb match (confidence: {left_confidence:.2f})")
                
                # Check right thumb match
                if right_thumb and biometric.right_thumb_fingerprint:
                    right_match, right_confidence = self._compare_templates(
                        right_thumb, biometric.right_thumb_fingerprint
                    )
                    if right_match:
                        match_score += 0.5
                        match_details.append(f"Right thumb match (confidence: {right_confidence:.2f})")
                
                # If we have a significant match, record it
                if match_score >= 0.5:  # At least one thumb matches
                    matches.append({
                        'tenant_id': tenant.id,
                        'tenant_name': tenant.name,
                        'citizen_id': biometric.citizen.id,
                        'citizen_name': f"{biometric.citizen.first_name} {biometric.citizen.last_name}",
                        'citizen_digital_id': biometric.citizen.digital_id,
                        'match_score': match_score,
                        'match_details': match_details,
                        'biometric_id': biometric.id
                    })
            
        except Exception as e:
            logger.error(f"Error checking tenant {tenant.name} for duplicates: {e}")
        
        return matches
    
    def _compare_templates(self, template1: str, template2: str) -> Tuple[bool, float]:
        """
        Compare two fingerprint templates.
        
        Args:
            template1 (str): First template
            template2 (str): Second template
            
        Returns:
            Tuple[bool, float]: (is_match, confidence_score)
        """
        try:
            # Simple comparison for development
            # In production, this would use proper biometric matching algorithms
            
            if template1 == template2:
                return True, 1.0
            
            # Mock similarity calculation based on template content
            # This is a placeholder - real implementation would use
            # proper minutiae matching algorithms
            
            try:
                # Try to decode and compare templates
                decoded1 = json.loads(base64.b64decode(template1).decode())
                decoded2 = json.loads(base64.b64decode(template2).decode())
                
                # Compare minutiae points (simplified)
                minutiae1 = decoded1.get('minutiae', [])
                minutiae2 = decoded2.get('minutiae', [])
                
                if not minutiae1 or not minutiae2:
                    return False, 0.0
                
                # Simple similarity based on minutiae count and positions
                similarity = self._calculate_minutiae_similarity(minutiae1, minutiae2)
                is_match = similarity >= self.match_threshold
                
                return is_match, similarity
                
            except:
                # Fallback to simple string comparison
                similarity = 0.3 + (hash(template1) % 100) / 100 * 0.4
                return similarity >= self.match_threshold, similarity
                
        except Exception as e:
            logger.error(f"Error comparing templates: {e}")
            return False, 0.0
    
    def _calculate_minutiae_similarity(self, minutiae1: List, minutiae2: List) -> float:
        """
        Calculate similarity between two sets of minutiae points.
        
        This is a simplified implementation for development.
        Production systems would use proper biometric matching algorithms.
        """
        if not minutiae1 or not minutiae2:
            return 0.0
        
        # Simple similarity based on minutiae count
        count_similarity = min(len(minutiae1), len(minutiae2)) / max(len(minutiae1), len(minutiae2))
        
        # Add some randomness for demo purposes
        import random
        position_similarity = random.uniform(0.6, 0.9)
        
        # Combine similarities
        overall_similarity = (count_similarity * 0.3) + (position_similarity * 0.7)
        
        return min(overall_similarity, 1.0)
    
    def get_biometric_statistics(self) -> Dict:
        """
        Get biometric statistics for the current tenant.

        Returns:
            Dict: Statistics about biometric data
        """
        try:
            # Import here to avoid circular imports
            from tenants.models.citizen import Biometric, Citizen

            # Count total citizens and biometric records
            total_citizens = Citizen.objects.count()
            total_biometrics = Biometric.objects.count()
            
            # Count by fingerprint availability
            try:
                left_thumb_count = Biometric.objects.exclude(
                    left_thumb_fingerprint__isnull=True
                ).exclude(left_thumb_fingerprint='').count()

                right_thumb_count = Biometric.objects.exclude(
                    right_thumb_fingerprint__isnull=True
                ).exclude(right_thumb_fingerprint='').count()

                both_thumbs_count = Biometric.objects.exclude(
                    left_thumb_fingerprint__isnull=True
                ).exclude(left_thumb_fingerprint='').exclude(
                    right_thumb_fingerprint__isnull=True
                ).exclude(right_thumb_fingerprint='').count()
            except Exception as query_error:
                logger.warning(f"Error querying biometric data: {query_error}")
                # Fallback to zero counts
                left_thumb_count = 0
                right_thumb_count = 0
                both_thumbs_count = 0
            
            # Calculate percentages
            biometric_coverage = (total_biometrics / total_citizens * 100) if total_citizens > 0 else 0
            left_thumb_coverage = (left_thumb_count / total_citizens * 100) if total_citizens > 0 else 0
            right_thumb_coverage = (right_thumb_count / total_citizens * 100) if total_citizens > 0 else 0
            complete_coverage = (both_thumbs_count / total_citizens * 100) if total_citizens > 0 else 0
            
            return {
                'total_citizens': total_citizens,
                'total_biometric_records': total_biometrics,
                'biometric_coverage_percent': round(biometric_coverage, 2),
                'fingerprint_stats': {
                    'left_thumb_count': left_thumb_count,
                    'right_thumb_count': right_thumb_count,
                    'both_thumbs_count': both_thumbs_count,
                    'left_thumb_coverage_percent': round(left_thumb_coverage, 2),
                    'right_thumb_coverage_percent': round(right_thumb_coverage, 2),
                    'complete_coverage_percent': round(complete_coverage, 2)
                },
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting biometric statistics: {e}")
            return {
                'error': str(e),
                'generated_at': datetime.now().isoformat()
            }
