import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Paper,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  Fingerprint as FingerprintIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Usb as UsbIcon,
  Speed as SpeedIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import biometricService from '../../services/biometricService';

const BiometricDemo = () => {
  const [deviceStatus, setDeviceStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [capturedFingerprints, setCapturedFingerprints] = useState({
    left: null,
    right: null
  });
  const [statistics, setStatistics] = useState(null);
  const [lastOperation, setLastOperation] = useState('');

  // Load initial data
  useEffect(() => {
    loadDeviceStatus();
    loadStatistics();
  }, []);

  const loadDeviceStatus = async () => {
    try {
      setLoading(true);
      const result = await biometricService.getDeviceStatus();
      if (result.success) {
        setDeviceStatus(result.data);
      } else {
        setError(result.error || 'Failed to get device status');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const result = await biometricService.getBiometricStats();
      if (result.success) {
        setStatistics(result.data);
      }
    } catch (err) {
      console.warn('Failed to load statistics:', err);
    }
  };

  const initializeDevice = async () => {
    try {
      setLoading(true);
      setError('');
      setLastOperation('Initializing device...');
      
      const result = await biometricService.initializeDevice();
      if (result.success) {
        setDeviceStatus(result.data);
        setLastOperation('Device initialized successfully');
      } else {
        setError(result.error || 'Device initialization failed');
        setLastOperation('Device initialization failed');
      }
    } catch (err) {
      setError(err.message);
      setLastOperation('Device initialization error');
    } finally {
      setLoading(false);
    }
  };

  const captureFingerprint = async (thumbType) => {
    try {
      setLoading(true);
      setError('');
      setLastOperation(`Capturing ${thumbType} thumb fingerprint...`);
      
      const result = await biometricService.captureFingerprint(thumbType);
      if (result.success) {
        setCapturedFingerprints(prev => ({
          ...prev,
          [thumbType]: result.data
        }));
        setLastOperation(`${thumbType} thumb captured successfully`);
      } else {
        setError(result.error || 'Fingerprint capture failed');
        setLastOperation(`${thumbType} thumb capture failed`);
      }
    } catch (err) {
      setError(err.message);
      setLastOperation(`${thumbType} thumb capture error`);
    } finally {
      setLoading(false);
    }
  };

  const validateFingerprint = async (thumbType) => {
    const fingerprint = capturedFingerprints[thumbType];
    if (!fingerprint) {
      setError(`No ${thumbType} thumb fingerprint to validate`);
      return;
    }

    try {
      setLoading(true);
      setError('');
      setLastOperation(`Validating ${thumbType} thumb quality...`);
      
      const result = await biometricService.validateFingerprintQuality(fingerprint.template_data);
      if (result.success) {
        setLastOperation(`${thumbType} thumb validation: ${result.data.is_valid ? 'PASSED' : 'FAILED'}`);
        if (!result.data.is_valid) {
          setError(`Quality issues: ${result.data.issues.join(', ')}`);
        }
      } else {
        setError(result.error || 'Validation failed');
        setLastOperation(`${thumbType} thumb validation failed`);
      }
    } catch (err) {
      setError(err.message);
      setLastOperation(`${thumbType} thumb validation error`);
    } finally {
      setLoading(false);
    }
  };

  const checkDuplicates = async () => {
    if (!capturedFingerprints.left && !capturedFingerprints.right) {
      setError('No fingerprints captured to check for duplicates');
      return;
    }

    try {
      setLoading(true);
      setError('');
      setLastOperation('Checking for duplicate fingerprints...');
      
      const duplicateData = {
        left_thumb_fingerprint: capturedFingerprints.left?.template_data,
        right_thumb_fingerprint: capturedFingerprints.right?.template_data
      };
      
      const result = await biometricService.checkDuplicateFingerprints(duplicateData);
      if (result.success) {
        const duplicates = result.data;
        if (duplicates.has_duplicates) {
          setLastOperation(`Found ${duplicates.total_matches} potential duplicate(s)`);
          setError(`Potential duplicates found in: ${duplicates.matches.map(m => m.tenant_name).join(', ')}`);
        } else {
          setLastOperation('No duplicates found - fingerprints are unique');
        }
      } else {
        setError(result.error || 'Duplicate check failed');
        setLastOperation('Duplicate check failed');
      }
    } catch (err) {
      setError(err.message);
      setLastOperation('Duplicate check error');
    } finally {
      setLoading(false);
    }
  };

  const clearFingerprints = () => {
    setCapturedFingerprints({ left: null, right: null });
    setLastOperation('Fingerprints cleared');
    setError('');
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Biometric System Demo
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" gutterBottom>
        Test the Futronic FS88H fingerprint integration functionality
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Device Status */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <UsbIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Device Status
              </Typography>
              
              {deviceStatus ? (
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      {deviceStatus.connected ? <CheckIcon color="success" /> : <ErrorIcon color="error" />}
                    </ListItemIcon>
                    <ListItemText 
                      primary="Connection" 
                      secondary={deviceStatus.connected ? 'Connected' : 'Disconnected'} 
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="Model" 
                      secondary={deviceStatus.model} 
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="Serial Number" 
                      secondary={deviceStatus.serial_number} 
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="Firmware" 
                      secondary={deviceStatus.firmware_version} 
                    />
                  </ListItem>
                </List>
              ) : (
                <Typography color="text.secondary">Loading device status...</Typography>
              )}
              
              <Box sx={{ mt: 2 }}>
                <Button 
                  variant="outlined" 
                  onClick={loadDeviceStatus}
                  disabled={loading}
                  sx={{ mr: 1 }}
                >
                  Refresh Status
                </Button>
                <Button 
                  variant="contained" 
                  onClick={initializeDevice}
                  disabled={loading}
                >
                  Initialize Device
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Fingerprint Capture */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <FingerprintIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Fingerprint Capture
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="subtitle2" gutterBottom>Left Thumb</Typography>
                    <FingerprintIcon 
                      sx={{ 
                        fontSize: 48, 
                        color: capturedFingerprints.left ? 'success.main' : 'grey.400',
                        mb: 1
                      }} 
                    />
                    {capturedFingerprints.left && (
                      <Chip 
                        icon={<CheckIcon />} 
                        label={`${capturedFingerprints.left.quality_score}%`}
                        color="success" 
                        size="small"
                        sx={{ mb: 1 }}
                      />
                    )}
                    <Box>
                      <Button 
                        size="small" 
                        variant="contained"
                        onClick={() => captureFingerprint('left')}
                        disabled={loading}
                        sx={{ mb: 1 }}
                      >
                        Capture
                      </Button>
                      {capturedFingerprints.left && (
                        <Button 
                          size="small" 
                          variant="outlined"
                          onClick={() => validateFingerprint('left')}
                          disabled={loading}
                        >
                          Validate
                        </Button>
                      )}
                    </Box>
                  </Paper>
                </Grid>
                
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="subtitle2" gutterBottom>Right Thumb</Typography>
                    <FingerprintIcon 
                      sx={{ 
                        fontSize: 48, 
                        color: capturedFingerprints.right ? 'success.main' : 'grey.400',
                        mb: 1
                      }} 
                    />
                    {capturedFingerprints.right && (
                      <Chip 
                        icon={<CheckIcon />} 
                        label={`${capturedFingerprints.right.quality_score}%`}
                        color="success" 
                        size="small"
                        sx={{ mb: 1 }}
                      />
                    )}
                    <Box>
                      <Button 
                        size="small" 
                        variant="contained"
                        onClick={() => captureFingerprint('right')}
                        disabled={loading}
                        sx={{ mb: 1 }}
                      >
                        Capture
                      </Button>
                      {capturedFingerprints.right && (
                        <Button 
                          size="small" 
                          variant="outlined"
                          onClick={() => validateFingerprint('right')}
                          disabled={loading}
                        >
                          Validate
                        </Button>
                      )}
                    </Box>
                  </Paper>
                </Grid>
              </Grid>
              
              <Box sx={{ mt: 2 }}>
                <Button 
                  variant="outlined" 
                  onClick={checkDuplicates}
                  disabled={loading || (!capturedFingerprints.left && !capturedFingerprints.right)}
                  sx={{ mr: 1 }}
                >
                  <SecurityIcon sx={{ mr: 1 }} />
                  Check Duplicates
                </Button>
                <Button 
                  variant="outlined" 
                  onClick={clearFingerprints}
                  disabled={loading}
                >
                  Clear All
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Statistics */}
        {statistics && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <AnalyticsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Biometric Statistics
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary.main">
                        {statistics.total_citizens}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Citizens
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="success.main">
                        {statistics.total_biometric_records}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Biometric Records
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="info.main">
                        {statistics.fingerprint_stats?.complete_coverage_percent || 0}%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Complete Coverage
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="warning.main">
                        {statistics.biometric_coverage_percent || 0}%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Overall Coverage
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Operation Log */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Operation Log
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {loading && <CircularProgress size={16} />}
                <Typography variant="body2" color={loading ? 'primary.main' : 'text.secondary'}>
                  {lastOperation || 'No operations performed yet'}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default BiometricDemo;
