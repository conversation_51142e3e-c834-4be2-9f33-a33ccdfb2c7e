import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Alert,
  CircularProgress,
} from '@mui/material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import citizenService from '../../services/citizenService';
import { useSharedData } from '../../contexts/SharedDataContext';
import { useAuth } from '../../contexts/AuthContext';
import FormStepper from '../../components/common/FormStepper';

// Import step components (we'll create these)
import PersonalInfoStep from './steps/PersonalInfoStep';
import AddressInfoStep from './steps/AddressInfoStep';
import FamilyInfoStep from './steps/FamilyInfoStep';
import EmergencyContactStep from './steps/EmergencyContactStep';
import AdditionalInfoStep from './steps/AdditionalInfoStep';
import PhotoCaptureStep from './steps/PhotoCaptureStep';
import BiometricCaptureStep from './steps/BiometricCaptureStep';
import ReviewStep from './steps/ReviewStep';

// Validation schema for each step
const stepValidationSchemas = {
  0: yup.object({
    first_name: yup.string().required('First name is required'),
    middle_name: yup.string().required('Middle name is required'),
    last_name: yup.string().required('Last name is required'),
    date_of_birth: yup.date().required('Date of birth is required'),
    gender: yup.string().required('Gender is required'),
    nationality: yup.string(),
    marital_status: yup.string(),
    religion: yup.string(),
    citizen_status: yup.string().required('Citizen status is required'),
  }),
  1: yup.object({
    phone_number: yup.string().matches(/^\+?[0-9]{10,15}$/, 'Invalid phone number'),
    email: yup.string().email('Invalid email address'),
    subcity: yup.string(),
    kebele: yup.string(),
    ketena: yup.string(),
    house_number: yup.string(),
  }),
  2: yup.object({
    // Parent information is now mandatory for fraud prevention
    father_first_name: yup.string().when('is_father_resident', {
      is: false,
      then: (schema) => schema.required('Father\'s first name is required'),
      otherwise: (schema) => schema
    }),
    father_last_name: yup.string().when('is_father_resident', {
      is: false,
      then: (schema) => schema.required('Father\'s last name is required'),
      otherwise: (schema) => schema
    }),
    father_id: yup.string().when('is_father_resident', {
      is: true,
      then: (schema) => schema.required('Father selection is required when resident'),
      otherwise: (schema) => schema
    }),
    mother_first_name: yup.string().when('is_mother_resident', {
      is: false,
      then: (schema) => schema.required('Mother\'s first name is required'),
      otherwise: (schema) => schema
    }),
    mother_last_name: yup.string().when('is_mother_resident', {
      is: false,
      then: (schema) => schema.required('Mother\'s last name is required'),
      otherwise: (schema) => schema
    }),
    mother_id: yup.string().when('is_mother_resident', {
      is: true,
      then: (schema) => schema.required('Mother selection is required when resident'),
      otherwise: (schema) => schema
    }),
  }),
  3: yup.object({
    emergency_contact_relation: yup.string().required('Relationship is required'),
    is_emergency_contact_resident: yup.boolean(),
    emergency_contact_id: yup.string().when('is_emergency_contact_resident', {
      is: true,
      then: (schema) => schema.required('Emergency contact ID is required when resident'),
      otherwise: (schema) => schema
    }),
    emergency_contact_first_name: yup.string().when('is_emergency_contact_resident', {
      is: false,
      then: (schema) => schema.required('Emergency contact first name is required'),
      otherwise: (schema) => schema
    }),
    emergency_contact_last_name: yup.string().when('is_emergency_contact_resident', {
      is: false,
      then: (schema) => schema.required('Emergency contact last name is required'),
      otherwise: (schema) => schema
    }),
    emergency_contact_phone: yup.string().when('is_emergency_contact_resident', {
      is: false,
      then: (schema) => schema.matches(/^\+?[0-9]{10,15}$/, 'Invalid phone number').required('Emergency contact phone is required'),
      otherwise: (schema) => schema
    }),
    emergency_contact_email: yup.string().when('is_emergency_contact_resident', {
      is: false,
      then: (schema) => schema.email('Invalid email address'),
      otherwise: (schema) => schema
    }),
  }),
  4: yup.object({
    employment: yup.string().required('Employment status is required'),
    employee_type: yup.string(),
    organization_name: yup.string(),
    region: yup.string(),
  }),
  5: yup.object({
    photo: yup.string().required('Photo is required for ID card generation'),
  }),
  6: yup.object({
    left_thumb_fingerprint: yup.string().required('Left thumb fingerprint is required'),
    right_thumb_fingerprint: yup.string().required('Right thumb fingerprint is required'),
  }),
  6: yup.object({
    left_thumb_fingerprint: yup.string().required('Left thumb fingerprint is required'),
    right_thumb_fingerprint: yup.string().required('Right thumb fingerprint is required'),
  }),
  7: yup.object({}), // Review step - no additional validation
};

const steps = [
  {
    label: 'Personal Information',
    description: 'Basic personal details',
  },
  {
    label: 'Contact Information',
    description: 'Contact details and address',
  },
  {
    label: 'Family Information',
    description: 'Spouse, parents, and children',
  },
  {
    label: 'Emergency Contact',
    description: 'Emergency contact details',
  },
  {
    label: 'Additional Information',
    description: 'Additional citizen details',
  },
  {
    label: 'Photo Capture',
    description: 'Capture or upload citizen photo',
  },
  {
    label: 'Biometric Capture',
    description: 'Capture left and right thumb fingerprints',
  },
  {
    label: 'Review & Submit',
    description: 'Review all information',
  },
];

const CitizenCreateStepper = () => {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const { user, checkAuth } = useAuth();
  const sharedData = useSharedData();
  const { loading: sharedDataLoading, maritalStatuses, subcities, kebeles } = sharedData;

  // Removed old search state - now using ResidentAutocomplete component

  const formik = useFormik({
    initialValues: {
      // Personal information
      first_name: '',
      middle_name: '',
      last_name: '',
      first_name_am: '',
      middle_name_am: '',
      last_name_am: '',
      date_of_birth: null,
      gender: '',
      phone_number: '',
      email: '',
      nationality: '',
      marital_status: '',
      blood_type: '',
      disability: 'none',
      religion: '',
      citizen_status: '',

      // Address information
      subcity: '',
      kebele: '',
      ketena: '',
      house_number: '',
      street: '',

      // Emergency contact
      is_emergency_contact_resident: false,
      emergency_contact_id: '',
      emergency_contact_first_name: '',
      emergency_contact_middle_name: '',
      emergency_contact_last_name: '',
      emergency_contact_phone: '',
      emergency_contact_email: '',
      emergency_contact_relation: '',

      // Spouse information
      has_spouse: false,
      is_spouse_resident: false,
      spouse_id: '',
      spouse_first_name: '',
      spouse_middle_name: '',
      spouse_last_name: '',
      spouse_first_name_am: '',
      spouse_middle_name_am: '',
      spouse_last_name_am: '',
      spouse_phone: '',
      spouse_email: '',

      // Parent information (now mandatory)
      has_parents: true,
      is_father_resident: false,
      father_id: '',
      father_first_name: '',
      father_middle_name: '',
      father_last_name: '',

      is_mother_resident: false,
      mother_id: '',
      mother_first_name: '',
      mother_middle_name: '',
      mother_last_name: '',

      // Children information
      has_children: false,
      children: [],

      // Additional information
      employment: '',
      employee_type: '',
      organization_name: '',
      region: '',

      // Photo
      photo: '',

      // Biometric data
      left_thumb_fingerprint: '',
      right_thumb_fingerprint: '',
    },
    validationSchema: stepValidationSchemas[activeStep],
    onSubmit: async (values) => {
      try {
        setLoading(true);
        setError('');

        console.log('🔍 Submitting citizen data:', values);
        console.log('🔍 Current user context:', user);

        // Check if user has tenant information
        if (!user || !user.tenant_id) {
          throw new Error('No tenant context found. Please ensure you are logged in to a tenant.');
        }

        const response = await citizenService.createCitizen(values);
        console.log('🔍 Citizen created successfully:', response);
        setSuccess(true);

        setTimeout(() => {
          navigate('/citizens');
        }, 2000);
      } catch (err) {
        console.error('Citizen creation error:', err);
        setError(err.response?.data?.message || err.message || 'An error occurred while registering the citizen');
      } finally {
        setLoading(false);
      }
    },
  });

  // Auto-fill subcity and kebele based on user's tenant
  useEffect(() => {
    console.log('🔍 Auto-fill useEffect triggered');
    console.log('🔍 User:', user);
    console.log('🔍 User tenant_id:', user?.tenant_id);
    console.log('🔍 User tenant:', user?.tenant);
    console.log('🔍 SharedData loading:', sharedDataLoading);
    console.log('🔍 Subcities data:', subcities);
    console.log('🔍 Kebeles data:', kebeles);
    console.log('🔍 Current form values:', {
      subcity: formik.values.subcity,
      kebele: formik.values.kebele
    });

    // Wait for both user and shared data to be loaded
    if (user && user.tenant_id && !sharedDataLoading && subcities && kebeles) {
      console.log('🔍 All data loaded, proceeding with auto-fill');
      console.log('🔍 User tenant info:', {
        tenant_id: user.tenant_id,
        tenant_name: user.tenant_name,
        tenant_type: user.tenant_type,
        parent_tenant_id: user.parent_tenant_id,
        parent_tenant_name: user.parent_tenant_name,
        parent_tenant_type: user.parent_tenant_type
      });
      console.log('🔍 Available subcities:', subcities.length);
      console.log('🔍 Available kebeles:', kebeles.length);

      // Auto-fill based on tenant type and hierarchy
      setTimeout(() => {
        try {
          if (user.tenant_type === 'kebele') {
            console.log('🔍 User is from kebele tenant - auto-filling both fields');

            // Find current kebele by tenant_id
            const currentKebele = kebeles.find(k => k.id === user.tenant_id);
            console.log('🔍 Found current kebele by ID:', currentKebele);

            if (currentKebele) {
              // Set kebele to current tenant
              formik.setFieldValue('kebele', currentKebele.id);
              console.log('🔍 Set kebele to:', currentKebele.id, currentKebele.name);

              // Set subcity based on the kebele's parent subcity
              if (currentKebele.sub_city) {
                formik.setFieldValue('subcity', currentKebele.sub_city);
                console.log('🔍 Set subcity to:', currentKebele.sub_city, currentKebele.subcity_name);
              } else if (user.parent_tenant_id) {
                // Use parent tenant from JWT token
                formik.setFieldValue('subcity', user.parent_tenant_id);
                console.log('🔍 Set subcity to parent from JWT:', user.parent_tenant_id, user.parent_tenant_name);
              }
            } else {
              console.log('🔍 Current kebele not found in kebeles list');
              console.log('🔍 Available kebeles:', kebeles.map(k => ({ id: k.id, name: k.name })));
            }

          } else if (user.tenant_type === 'subcity') {
            console.log('🔍 User is from subcity tenant - auto-filling subcity only');

            // Find current subcity by tenant_id
            const currentSubcity = subcities.find(s => s.id === user.tenant_id);
            console.log('🔍 Found current subcity by ID:', currentSubcity);

            if (currentSubcity) {
              formik.setFieldValue('subcity', currentSubcity.id);
              console.log('🔍 Set subcity to:', currentSubcity.id, currentSubcity.name);
            } else {
              console.log('🔍 Current subcity not found in subcities list');
              console.log('🔍 Available subcities:', subcities.map(s => ({ id: s.id, name: s.name })));
            }

          } else if (user.tenant_type === 'city') {
            console.log('🔍 User is from city tenant - no auto-fill needed');
            // City users can select any subcity and kebele

          } else {
            console.log('🔍 Unknown tenant type or no tenant:', user.tenant_type);
            console.log('🔍 User object:', user);
          }

          console.log('🔍 Auto-fill completed');
        } catch (error) {
          console.error('🔍 Error during auto-fill:', error);
        }
      }, 100);
    } else {
      console.log('🔍 Waiting for data...', {
        hasUser: !!user,
        hasTenantId: !!user?.tenant_id,
        sharedDataLoading,
        hasSubcities: !!subcities,
        hasKebeles: !!kebeles
      });

      // Error handling for missing tenant data
      if (user && !user.tenant_id && !sharedDataLoading) {
        console.warn('🔍 Warning: User has no tenant_id. This may indicate:');
        console.warn('  - JWT token missing tenant information');
        console.warn('  - User not assigned to any tenant');
        console.warn('  - Authentication backend not setting tenant context');
        console.warn('  - Old JWT token format (user may need to log out and log back in)');

        // Try to refresh the token automatically
        console.log('🔍 Attempting to refresh token to get tenant information...');
        checkAuth().then((success) => {
          if (success) {
            console.log('🔍 Token refreshed successfully');
          } else {
            console.log('🔍 Token refresh failed');
            // Show a user-friendly message with refresh suggestion
            if (!error) {
              setError('Unable to determine your tenant context. Please try logging out and back in.');
            }
          }
        }).catch((err) => {
          console.error('🔍 Error refreshing token:', err);
          if (!error) {
            setError('Unable to determine your tenant context. Please try logging out and back in.');
          }
        });
      }
    }
  }, [user, sharedDataLoading, subcities, kebeles]);

  // Watch for marital status changes and reset spouse info if single
  useEffect(() => {
    // Only proceed if shared data is loaded
    if (!maritalStatuses) return;

    // Find the "Single" marital status from shared data
    const singleStatus = maritalStatuses.find(status =>
      status.name?.toLowerCase() === 'single'
    );
    const singleStatusId = singleStatus?.id;

    if (formik.values.marital_status === singleStatusId ||
        formik.values.marital_status === "" ||
        !formik.values.marital_status) {
      // Reset spouse information when marital status is single or empty
      formik.setValues(prev => ({
        ...prev,
        has_spouse: false,
        is_spouse_resident: false,
        spouse_id: '',
        spouse_first_name: '',
        spouse_middle_name: '',
        spouse_last_name: '',
        spouse_first_name_am: '',
        spouse_middle_name_am: '',
        spouse_last_name_am: '',
        spouse_phone: '',
        spouse_email: '',
      }));
    }
  }, [formik.values.marital_status, maritalStatuses]);

  // Removed old search functions - now using ResidentAutocomplete component

  // Function to add a child
  const addChild = () => {
    formik.setValues({
      ...formik.values,
      children: [
        ...formik.values.children,
        {
          is_resident: false,
          child_id: '',
          first_name: '',
          middle_name: '',
          last_name: '',
          date_of_birth: null,
          gender: ''
        }
      ]
    });
  };

  // Function to remove a child
  const removeChild = (index) => {
    const updatedChildren = [...formik.values.children];
    updatedChildren.splice(index, 1);
    formik.setValues({
      ...formik.values,
      children: updatedChildren
    });
  };

  const handleNext = async () => {
    // Validate current step
    const currentStepSchema = stepValidationSchemas[activeStep];
    try {
      await currentStepSchema.validate(formik.values, { abortEarly: false });
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    } catch (validationError) {
      // Set formik errors
      const errors = {};
      validationError.inner.forEach((error) => {
        errors[error.path] = error.message;
      });
      formik.setErrors(errors);
      formik.setTouched(
        validationError.inner.reduce((touched, error) => {
          touched[error.path] = true;
          return touched;
        }, {})
      );
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const canProceed = () => {
    const currentStepSchema = stepValidationSchemas[activeStep];
    if (!currentStepSchema) {
      console.warn(`⚠️ No validation schema found for step ${activeStep}`);
      return true; // Allow proceeding if no schema is defined
    }

    try {
      currentStepSchema.validateSync(formik.values, { abortEarly: false });
      console.log(`✅ Step ${activeStep} validation passed`);
      return true;
    } catch (validationError) {
      console.log(`❌ Step ${activeStep} validation failed:`, validationError.errors);

      // For biometric step, log specific values
      if (activeStep === 6) {
        console.log('🔍 Biometric step values:', {
          leftThumb: Boolean(formik.values.left_thumb_fingerprint),
          rightThumb: Boolean(formik.values.right_thumb_fingerprint),
          leftThumbData: formik.values.left_thumb_fingerprint?.substring(0, 50) + '...',
          rightThumbData: formik.values.right_thumb_fingerprint?.substring(0, 50) + '...'
        });
      }

      return false;
    }
  };

  const renderStepContent = (step) => {
    const commonProps = {
      formik,
      loading: loading || sharedDataLoading,
      addChild,
      removeChild,
    };

    switch (step) {
      case 0:
        return <PersonalInfoStep {...commonProps} />;
      case 1:
        return <AddressInfoStep {...commonProps} />;
      case 2:
        return <FamilyInfoStep {...commonProps} />;
      case 3:
        return <EmergencyContactStep {...commonProps} />;
      case 4:
        return <AdditionalInfoStep {...commonProps} />;
      case 5:
        return <PhotoCaptureStep {...commonProps} />;
      case 6:
        return <BiometricCaptureStep {...commonProps} />;
      case 7:
        return <ReviewStep {...commonProps} />;
      default:
        return <div>Unknown step</div>;
    }
  };

  if (success) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Alert severity="success" sx={{ mb: 2 }}>
          Citizen registered successfully! Redirecting...
        </Alert>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Register New Citizen
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <FormStepper
        steps={steps}
        activeStep={activeStep}
        onNext={handleNext}
        onBack={handleBack}
        onSubmit={formik.handleSubmit}
        isLastStep={activeStep === steps.length - 1}
        isFirstStep={activeStep === 0}
        canProceed={canProceed()}
        loading={loading}
      >
        {renderStepContent(activeStep)}
      </FormStepper>
    </Box>
  );
};

export default CitizenCreateStepper;
