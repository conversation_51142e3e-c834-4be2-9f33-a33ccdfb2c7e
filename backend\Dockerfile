FROM python:3.10-slim

WORKDIR /app

# Install system dependencies for USB device access and Futronic SDK
RUN apt-get update && apt-get install -y \
    libusb-1.0-0-dev \
    udev \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Download and install Futronic SDK
RUN wget -O /tmp/futronic-sdk.tar.gz "https://www.futronic-tech.com/downloads/linux-sdk.tar.gz" \
    && tar -xzf /tmp/futronic-sdk.tar.gz -C /opt/ \
    && rm /tmp/futronic-sdk.tar.gz

# Set environment variables for SDK
ENV FUTRONIC_SDK_PATH=/opt/futronic-sdk
ENV LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/opt/futronic-sdk/lib

# Copy project
COPY . .

# Make startup script executable
RUN chmod +x /app/startup.sh

EXPOSE 8000

CMD ["bash", "/app/startup.sh"]
