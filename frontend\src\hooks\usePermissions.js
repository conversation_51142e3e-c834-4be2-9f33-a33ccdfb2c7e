import { useAuth } from '../contexts/AuthContext';

// Map frontend permission names to backend general permission codenames
const PERMISSION_MAPPING = {
  // Dashboard - based on role level
  'view_dashboard': ['view_kebele_dashboard', 'view_subcity_dashboard', 'view_city_dashboard', 'view_system_dashboard'],

  // Citizens - workflow-based permissions
  'view_citizens': ['view_citizens_list', 'view_own_kebele_data', 'view_child_kebeles_data', 'view_child_subcities_data', 'view_all_tenants_data'],
  'manage_citizens': ['view_citizens_list', 'view_citizen_details', 'view_child_kebeles_data'], // Subcity admins can manage (view/oversee) citizens
  'register_citizens': ['register_citizens'], // Only clerks can register

  // ID Cards - workflow-based permissions
  'view_idcards': ['view_id_cards_list', 'view_own_kebele_data', 'view_child_kebeles_data', 'view_child_subcities_data'],
  'manage_idcards': ['view_id_cards_list', 'approve_id_cards'], // Subcity admins can manage (view/approve) ID cards
  'create_idcards': ['generate_id_cards'], // Only clerks can generate
  'approve_idcards': ['approve_id_cards'],
  'print_idcards': ['generate_id_cards', 'approve_id_cards', 'print_id_cards'], // Users who can generate, approve, or have print permission

  // Tenants - system administration
  'view_tenants': ['manage_tenants', 'full_system_access'],
  'manage_tenants': ['manage_tenants', 'full_system_access'],
  'create_tenants': ['manage_tenants', 'full_system_access'],
  'edit_tenants': ['manage_tenants', 'full_system_access'],
  'delete_tenants': ['manage_tenants', 'full_system_access'],

  // Users - hierarchical user management
  'view_users': ['create_kebele_users', 'create_subcity_users', 'manage_all_users'],
  'manage_users': ['create_kebele_users', 'create_subcity_users', 'manage_all_users'],
  'create_users': ['create_kebele_users', 'create_subcity_users', 'manage_all_users'],

  // Reports - hierarchical reporting
  'view_reports': ['view_kebele_reports', 'view_subcity_reports', 'view_city_reports', 'view_all_reports'],

  // Workflows - document verification and processing
  'view_workflows': ['verify_documents', 'approve_id_cards'],

  // System settings (only for high-level admins)
  'system_settings': ['manage_system_settings', 'full_system_access'],

  // Cross-tenant views (hierarchical data access)
  'view_cross_kebele': ['view_child_kebeles_data'], // Subcity admins can view child kebeles
  'view_cross_subcity': ['view_child_subcities_data'], // City admins can view child subcities

  // Document verification
  'verify_documents': ['verify_documents'],

  // Transfer citizens
  'transfer_citizens': ['verify_documents', 'approve_id_cards'],

  // Clearances - flexible permissions for any group
  'create_clearances': ['verify_documents', 'view_citizens_list'], // Can create clearance requests
  'view_clearances': ['view_citizens_list'], // Can view clearance lists
  'manage_clearances': ['verify_documents', 'view_citizens_list'], // Can manage clearances

  // Transfers - flexible permissions for any group
  'create_transfers': ['verify_documents', 'view_citizens_list'], // Can create transfer requests
  'view_transfers': ['view_citizens_list'], // Can view transfer lists
  'manage_transfers': ['verify_documents', 'view_citizens_list'] // Can manage transfers
};

// Role-based permissions aligned with new general permission system
const ROLE_PERMISSIONS = {
  superadmin: [
    'view_dashboard',
    'manage_tenants',
    'view_tenants',
    'create_tenants',
    'edit_tenants',
    'delete_tenants',
    'view_citizens',
    'manage_citizens',
    'register_citizens',
    'view_idcards',
    'manage_idcards',
    'create_idcards',
    'approve_idcards',
    'print_idcards',
    'view_reports',
    'manage_users',
    'create_users',
    'view_workflows',
    'verify_documents',
    'system_settings',
    'view_cross_kebele',
    'view_cross_subcity'
  ],
  city_admin: [
    'view_dashboard',
    'view_citizens',
    'manage_citizens', // Can manage citizens city-wide
    'view_idcards',
    'manage_idcards', // Can manage ID cards city-wide
    'view_reports',
    'manage_users', // Can create subcity users
    'create_users',
    'view_workflows',
    'view_cross_subcity' // Can view data from child subcities
  ],
  subcity_admin: [
    'view_dashboard',
    'view_citizens',
    'manage_citizens', // Can manage (view/oversee) citizens from child kebeles
    'view_idcards',
    'manage_idcards', // Can manage (view/approve) ID cards
    'approve_idcards',
    'view_reports',
    'manage_users', // Can create kebele users
    'create_users',
    'view_workflows',
    'view_cross_kebele' // Can view data from child kebeles
  ],
  kebele_admin: [
    'view_dashboard',
    'view_citizens',
    'register_citizens',
    'view_idcards',
    'create_idcards',
    'approve_idcards',
    'view_reports',
    'manage_users', // Can manage users in their kebele
    'create_users',
    'view_workflows',
    'verify_documents'
  ],
  kebele_leader: [
    'view_dashboard',
    'view_citizens',
    'view_idcards',
    'approve_idcards',
    'transfer_citizens',
    'view_reports',
    'verify_documents',
    // Clearance permissions
    'create_clearances',
    'view_clearances',
    'manage_clearances',
    // Transfer permissions
    'create_transfers',
    'view_transfers',
    'manage_transfers'
    // Note: Kebele leaders can approve and verify but cannot create citizens/ID cards or manage users
  ],
  clerk: [
    'view_dashboard',
    'view_citizens',
    'register_citizens',
    'view_idcards',
    'create_idcards',
    // Clearance permissions (basic level)
    'create_clearances',
    'view_clearances',
    // Transfer permissions (basic level)
    'create_transfers',
    'view_transfers'
    // Note: Clerks have basic operational permissions:
    // - Can register citizens (full workflow)
    // - Can generate ID cards
    // - Can view their kebele dashboard
    // - Can create clearances and transfers (but not manage them)
    // - Cannot approve, manage users, or view reports
  ]
};

// Define navigation items with required permissions
export const NAVIGATION_ITEMS = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    path: '/dashboard',
    icon: 'Dashboard',
    permission: 'view_dashboard'
  },
  {
    id: 'citizens',
    label: 'Citizens',
    path: '/citizens',
    icon: 'People',
    permission: 'view_citizens',
    children: [
      {
        id: 'citizens-list',
        label: 'Citizens List',
        path: '/citizens',
        permission: 'view_citizens'
      },
      {
        id: 'citizens-register',
        label: 'Register Citizen',
        path: '/citizens/register',
        permission: 'register_citizens'
      }
    ]
  },
  {
    id: 'idcards',
    label: 'ID Cards',
    path: '/idcards',
    icon: 'Badge',
    permission: 'view_idcards',
    children: [
      {
        id: 'idcards-list',
        label: 'ID Cards List',
        path: '/idcards',
        permission: 'view_idcards'
      },
      {
        id: 'idcards-pending',
        label: 'Pending Approval',
        path: '/idcards/pending',
        permission: 'approve_idcards'
      },
      {
        id: 'idcards-printing',
        label: 'Printing Queue',
        path: '/idcards/printing-queue',
        permission: 'print_idcards'
      }
    ]
  },
  {
    id: 'tenants',
    label: 'Tenants',
    path: '/tenants',
    icon: 'Business',
    permission: 'view_tenants',
    children: [
      {
        id: 'tenants-list',
        label: 'Tenants List',
        path: '/tenants',
        permission: 'view_tenants'
      },
      {
        id: 'tenants-register',
        label: 'Register Tenant',
        path: '/tenants/register',
        permission: 'create_tenants'
      }
    ]
  },
  {
    id: 'reports',
    label: 'Reports',
    path: '/reports',
    icon: 'Assessment',
    permission: 'view_reports'
  },
  {
    id: 'users',
    label: 'Users',
    path: '/users',
    icon: 'SupervisorAccount',
    permission: 'manage_users',
    children: [
      {
        id: 'users-list',
        label: 'Users List',
        path: '/users',
        permission: 'manage_users'
      },
      {
        id: 'users-kebele',
        label: 'Kebele Management',
        path: '/users/kebele-management',
        permission: 'manage_users'
      },
      {
        id: 'users-permissions',
        label: 'Permissions',
        path: '/users/permissions',
        permission: 'manage_users'
      }
    ]
  },
  {
    id: 'workflows',
    label: 'Workflows',
    path: '/workflows',
    icon: 'Timeline',
    permission: 'view_workflows',
    children: [
      {
        id: 'workflows-verification',
        label: 'Document Verification',
        path: '/workflows/verification',
        permission: 'verify_documents'
      },
      {
        id: 'workflows-transfers',
        label: 'Citizen Transfers',
        path: '/workflows/transfers',
        permission: 'transfer_citizens'
      }
    ]
  },
  {
    id: 'settings',
    label: 'Settings',
    path: '/settings',
    icon: 'Settings',
    permission: 'system_settings'
  }
];

export const usePermissions = () => {
  const { user } = useAuth();

  const hasPermission = (permission) => {
    if (!user) {
      console.log(`❌ Permission check failed: No user`);
      return false;
    }

    // Super admin has all permissions
    if (user.is_superuser || user.role === 'superadmin') {
      console.log(`✅ Permission granted: ${permission} (superuser)`);
      return true;
    }

    // Use actual permissions from backend if available (GROUP-BASED SYSTEM)
    if (user.permissions && Array.isArray(user.permissions)) {
      const requiredPermissions = PERMISSION_MAPPING[permission] || [permission];

      // Check if user has any of the required backend permissions
      // user.permissions is already an array of permission codenames (strings)
      const userPermissionCodes = user.permissions;
      const hasAccess = requiredPermissions.some(reqPerm => userPermissionCodes.includes(reqPerm));

      console.log(`🔍 Group-based permission check: ${permission}`, {
        required: requiredPermissions,
        userHas: userPermissionCodes,
        userGroups: user.groups || [],
        result: hasAccess
      });

      return hasAccess;
    }

    // Fallback to role-based permissions if user.permissions not available (LEGACY SYSTEM)
    if (user.role) {
      const userPermissions = ROLE_PERMISSIONS[user.role] || [];
      const hasAccess = userPermissions.includes(permission);

      console.log(`🔍 Legacy role-based permission check: ${permission}`, {
        role: user.role,
        rolePermissions: userPermissions,
        result: hasAccess
      });

      return hasAccess;
    }

    console.log(`❌ Permission check failed: ${permission} (no role or permissions)`);
    return false;
  };

  const getCustomGroupPermissions = () => {
    if (!user || !user.groups) return [];

    // Filter out system groups to identify custom groups
    const systemGroups = ['clerk', 'kebele_leader', 'subcity_admin', 'city_admin', 'super_admin'];
    const customGroups = user.groups.filter(group => !systemGroups.includes(group));

    return customGroups;
  };

  const isUsingCustomGroups = () => {
    const customGroups = getCustomGroupPermissions();
    return customGroups.length > 0;
  };

  const hasAnyPermission = (permissions) => {
    return permissions.some(permission => hasPermission(permission));
  };

  const getFilteredNavigationItems = () => {
    return NAVIGATION_ITEMS.filter(item => {
      // Check if user has permission for main item
      if (!hasPermission(item.permission)) return false;

      // Filter children based on permissions
      if (item.children) {
        item.children = item.children.filter(child => hasPermission(child.permission));
      }

      return true;
    });
  };

  const getDynamicNavigationItems = () => {
    console.log('🔍 getDynamicNavigationItems called:', {
      user: user ? { username: user.username, role: user.role, permissions: user.permissions } : null,
      hasUser: Boolean(user),
      hasPermissions: Boolean(user?.permissions),
      permissionsCount: user?.permissions?.length || 0
    });

    // Quick fix: Return basic navigation for authenticated users
    if (!user) return [];

    const basicItems = [
      {
        id: 'dashboard',
        label: 'Dashboard',
        path: '/dashboard',
        icon: 'Dashboard',
        permission: 'view_dashboard'
      }
    ];

    // Add Citizens if user has citizen permissions
    if (user.permissions && user.permissions.some(p => p.includes('citizen'))) {
      const citizensItem = {
        id: 'citizens',
        label: 'Citizens',
        icon: 'People',
        permission: 'view_citizens',
        children: []
      };

      // Determine path based on permissions
      if (user.permissions.includes('view_child_subcities_data')) {
        citizensItem.path = '/citizens/citizen-book';
        citizensItem.label = 'Citizen Directory';
      } else if (user.permissions.includes('view_child_kebeles_data')) {
        citizensItem.path = '/citizens/all-kebeles';
      } else {
        citizensItem.path = '/citizens';
      }

      // Add register citizen if user can register
      if (user.permissions.includes('register_citizens')) {
        citizensItem.children.push({
          id: 'citizens-register',
          label: 'Register Citizen',
          path: '/citizens/create',
          permission: 'register_citizens'
        });
      }

      basicItems.push(citizensItem);
    }

    // Add ID Cards if user has ID card permissions
    if (user.permissions && user.permissions.some(p => p.includes('id_card'))) {
      const idCardsItem = {
        id: 'idcards',
        label: 'ID Cards',
        icon: 'Badge',
        permission: 'view_idcards',
        children: []
      };

      // Determine path
      if (user.permissions.includes('view_child_kebeles_data')) {
        idCardsItem.path = '/idcards/all-kebeles';
      } else {
        idCardsItem.path = '/idcards';
      }

      // Add children based on permissions
      if (user.permissions.includes('generate_id_cards')) {
        idCardsItem.children.push({
          id: 'idcards-create',
          label: 'Generate ID Cards',
          path: '/idcards/create',
          permission: 'create_idcards'
        });
      }

      if (user.permissions.includes('approve_id_cards')) {
        idCardsItem.children.push({
          id: 'idcards-pending',
          label: 'Pending Approval',
          path: '/idcards/pending',
          permission: 'approve_idcards'
        });
      }

      if (user.permissions.includes('print_id_cards')) {
        idCardsItem.children.push({
          id: 'idcards-printing',
          label: 'Printing Queue',
          path: '/idcards/printing-queue',
          permission: 'print_idcards'
        });
      }

      basicItems.push(idCardsItem);
    }

    // Add Users if user has user management permissions
    if (user.permissions && user.permissions.some(p => p.includes('user'))) {
      const usersItem = {
        id: 'users',
        label: 'Users',
        icon: 'SupervisorAccount',
        permission: 'manage_users',
        children: []
      };

      // Determine path based on permissions
      if (user.permissions.includes('manage_all_users')) {
        usersItem.path = '/users';
        usersItem.label = 'System Users';
      } else if (user.permissions.includes('create_subcity_users')) {
        usersItem.path = '/users/city-management';
        usersItem.label = 'City Users';
      } else {
        usersItem.path = '/users/kebele-management';
        usersItem.label = 'Kebele Users';
      }

      basicItems.push(usersItem);
    }

    // Add Reports if user has report permissions
    if (user.permissions && user.permissions.some(p => p.includes('report'))) {
      basicItems.push({
        id: 'reports',
        label: 'Reports',
        path: '/reports',
        icon: 'Assessment',
        permission: 'view_reports'
      });
    }

    // Add Clearances menu - permission-based (works with any group/role that has clearance permissions)
    if (hasPermission('create_clearances') || hasPermission('view_clearances') || hasPermission('manage_clearances')) {
      basicItems.push({
        id: 'clearances',
        label: 'Clearances',
        path: '/clearances',
        icon: 'Assignment',
        permission: 'create_clearances'
      });
    }

    // Add Transfers menu - permission-based (works with any group/role that has transfer permissions)
    if (hasPermission('create_transfers') || hasPermission('view_transfers') || hasPermission('manage_transfers')) {
      basicItems.push({
        id: 'transfers',
        label: 'Transfers',
        path: '/transfers',
        icon: 'SwapHoriz',
        permission: 'create_transfers'
      });
    }

    console.log('✅ Generated navigation items:', basicItems);
    return basicItems;

    // End of function - return the basic items
  };

  const canAccess = (path) => {
    const item = NAVIGATION_ITEMS.find(nav =>
      nav.path === path ||
      (nav.children && nav.children.some(child => child.path === path))
    );

    if (!item) return true; // Allow access to unprotected routes

    if (item.children) {
      const childItem = item.children.find(child => child.path === path);
      return childItem ? hasPermission(childItem.permission) : hasPermission(item.permission);
    }

    return hasPermission(item.permission);
  };

  return {
    hasPermission,
    hasAnyPermission,
    getFilteredNavigationItems,
    getDynamicNavigationItems,
    canAccess,
    getCustomGroupPermissions,
    isUsingCustomGroups,
    userRole: user?.role,
    userPermissions: user?.permissions || [],
    userGroups: user?.groups || [],
    customGroups: getCustomGroupPermissions(),
    isAdmin: user?.role?.includes('admin') || user?.is_superuser,
    isSuperAdmin: user?.is_superuser || user?.role === 'superadmin'
  };
};
