import axios from '../utils/axios';

class BiometricService {
  constructor() {
    this.baseURL = '/api/tenants/biometrics';
  }

  /**
   * Create biometric record for a citizen
   * @param {Object} biometricData - Biometric data including fingerprints
   * @returns {Promise} API response
   */
  async createBiometric(biometricData) {
    try {
      const response = await axios.post(`${this.baseURL}/`, biometricData);
      return response.data;
    } catch (error) {
      console.error('Error creating biometric record:', error);
      throw error;
    }
  }

  /**
   * Update biometric record
   * @param {number} biometricId - Biometric record ID
   * @param {Object} biometricData - Updated biometric data
   * @returns {Promise} API response
   */
  async updateBiometric(biometricId, biometricData) {
    try {
      const response = await axios.put(`${this.baseURL}/${biometricId}/`, biometricData);
      return response.data;
    } catch (error) {
      console.error('Error updating biometric record:', error);
      throw error;
    }
  }

  /**
   * Get biometric record by citizen ID
   * @param {number} citizenId - Citizen ID
   * @returns {Promise} API response
   */
  async getBiometricByCitizen(citizenId) {
    try {
      const response = await axios.get(`${this.baseURL}/?citizen=${citizenId}`);
      return response.data.results?.[0] || null;
    } catch (error) {
      console.error('Error fetching biometric record:', error);
      throw error;
    }
  }

  /**
   * Validate fingerprint quality
   * @param {string} fingerprintData - Base64 encoded fingerprint template
   * @returns {Promise} Validation result
   */
  async validateFingerprintQuality(fingerprintData) {
    try {
      const response = await axios.post('/api/biometrics/validate-quality/', {
        fingerprint_data: fingerprintData
      });
      return response.data;
    } catch (error) {
      console.error('Error validating fingerprint quality:', error);
      throw error;
    }
  }

  /**
   * Check for duplicate fingerprints across tenants
   * @param {Object} fingerprintData - Fingerprint data to check
   * @returns {Promise} Duplicate check result
   */
  async checkDuplicateFingerprints(fingerprintData) {
    try {
      const response = await axios.post('/api/biometrics/check-duplicates/', fingerprintData);
      return response.data;
    } catch (error) {
      console.error('Error checking duplicate fingerprints:', error);
      throw error;
    }
  }

  /**
   * Get device status
   * @returns {Promise} Device status
   */
  async getDeviceStatus() {
    try {
      const response = await axios.get('/api/biometrics/device-status/');
      return response.data;
    } catch (error) {
      console.error('Error getting device status:', error);
      throw error;
    }
  }

  /**
   * Initialize fingerprint device
   * @returns {Promise} Initialization result
   */
  async initializeDevice() {
    try {
      const response = await axios.post('/api/biometrics/initialize-device/');
      return response.data;
    } catch (error) {
      console.error('Error initializing device:', error);
      throw error;
    }
  }

  /**
   * Capture fingerprint from device
   * @param {string} thumbType - 'left' or 'right'
   * @returns {Promise} Capture result
   */
  async captureFingerprint(thumbType) {
    try {
      const response = await axios.post('/api/biometrics/capture/', {
        thumb_type: thumbType
      });
      return response.data;
    } catch (error) {
      console.error('Error capturing fingerprint:', error);
      throw error;
    }
  }

  /**
   * Process raw fingerprint data into template
   * @param {string} rawData - Raw fingerprint data
   * @param {string} thumbType - 'left' or 'right'
   * @returns {Promise} Processing result
   */
  async processFingerprint(rawData, thumbType) {
    try {
      const response = await axios.post('/api/biometrics/process/', {
        raw_data: rawData,
        thumb_type: thumbType
      });
      return response.data;
    } catch (error) {
      console.error('Error processing fingerprint:', error);
      throw error;
    }
  }

  /**
   * Match fingerprints for verification
   * @param {string} template1 - First fingerprint template
   * @param {string} template2 - Second fingerprint template
   * @returns {Promise} Match result
   */
  async matchFingerprints(template1, template2) {
    try {
      const response = await axios.post('/api/biometrics/match/', {
        template1,
        template2
      });
      return response.data;
    } catch (error) {
      console.error('Error matching fingerprints:', error);
      throw error;
    }
  }

  /**
   * Get biometric statistics
   * @returns {Promise} Statistics data
   */
  async getBiometricStats() {
    try {
      const response = await axios.get('/api/biometrics/stats/');
      return response.data;
    } catch (error) {
      console.error('Error fetching biometric statistics:', error);
      throw error;
    }
  }

  /**
   * Export biometric data (for backup/migration)
   * @param {Array} citizenIds - Array of citizen IDs to export
   * @returns {Promise} Export data
   */
  async exportBiometricData(citizenIds = []) {
    try {
      const response = await axios.post('/api/biometrics/export/', {
        citizen_ids: citizenIds
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting biometric data:', error);
      throw error;
    }
  }

  /**
   * Import biometric data (for backup/migration)
   * @param {Object} biometricData - Biometric data to import
   * @returns {Promise} Import result
   */
  async importBiometricData(biometricData) {
    try {
      const response = await axios.post('/api/biometrics/import/', biometricData);
      return response.data;
    } catch (error) {
      console.error('Error importing biometric data:', error);
      throw error;
    }
  }

  /**
   * Delete biometric record
   * @param {number} biometricId - Biometric record ID
   * @returns {Promise} Deletion result
   */
  async deleteBiometric(biometricId) {
    try {
      const response = await axios.delete(`${this.baseURL}/${biometricId}/`);
      return response.data;
    } catch (error) {
      console.error('Error deleting biometric record:', error);
      throw error;
    }
  }

  /**
   * Get supported biometric types
   * @returns {Promise} Supported types
   */
  async getSupportedTypes() {
    try {
      const response = await axios.get('/api/shared/biometric-types/');
      return response.data;
    } catch (error) {
      console.error('Error fetching biometric types:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const biometricService = new BiometricService();
export default biometricService;
