import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  LinearProgress,
  Chip,
  Paper,
} from '@mui/material';
import {
  Fingerprint as FingerprintIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Close as CloseIcon,
  Refresh as RefreshIcon,
  Usb as UsbIcon,
} from '@mui/icons-material';
import FingerprintCapture from '../../../components/biometrics/FingerprintCapture';
import biometricService from '../../../services/biometricService';

const BiometricCaptureStep = ({ formik, loading }) => {
  const [deviceConnected, setDeviceConnected] = useState(false);
  const [deviceError, setDeviceError] = useState('');
  const [captureDialog, setCaptureDialog] = useState(false);
  const [currentCapture, setCurrentCapture] = useState(null); // 'left' or 'right'
  const [captureProgress, setCaptureProgress] = useState(0);
  const [captureStatus, setCaptureStatus] = useState(''); // 'capturing', 'processing', 'complete', 'error'

  // Check device connection on component mount
  useEffect(() => {
    checkDeviceConnection();
  }, []);

  const checkDeviceConnection = useCallback(async () => {
    try {
      setDeviceError('');

      // Check if Web Serial API is supported
      if (!('serial' in navigator)) {
        console.warn('Web Serial API not supported, using mock device');
        setDeviceConnected(true); // Allow mock operation
        return;
      }

      // Try to get device status from API
      try {
        const statusResult = await biometricService.getDeviceStatus();
        if (statusResult.success) {
          const deviceStatus = statusResult.data;
          setDeviceConnected(deviceStatus.connected && deviceStatus.initialized);

          if (!deviceStatus.connected) {
            // Try to initialize device
            const initResult = await biometricService.initializeDevice();
            if (initResult.success) {
              setDeviceConnected(true);
            } else {
              throw new Error(initResult.error || 'Device initialization failed');
            }
          }
        } else {
          throw new Error(statusResult.error || 'Failed to get device status');
        }
      } catch (apiError) {
        console.warn('API device check failed, using mock device:', apiError);
        // Fallback to mock device for development
        setDeviceConnected(true);
      }
    } catch (error) {
      console.error('Device connection error:', error);
      setDeviceError(error.message);
      setDeviceConnected(false);
    }
  }, []);

  const startCapture = useCallback((thumbType) => {
    setCurrentCapture(thumbType);
    setCaptureDialog(true);
    setCaptureProgress(0);
    setCaptureStatus('capturing');
  }, []);

  const handleCaptureComplete = useCallback((thumbType, fingerprintData) => {
    // Store the fingerprint data in formik
    const fieldName = `${thumbType}_thumb_fingerprint`;
    formik.setFieldValue(fieldName, fingerprintData);
    
    setCaptureDialog(false);
    setCurrentCapture(null);
    setCaptureStatus('complete');
  }, [formik]);

  const handleCaptureError = useCallback((error) => {
    console.error('Fingerprint capture error:', error);
    setCaptureStatus('error');
    setDeviceError(error.message);
  }, []);

  const retryCapture = useCallback(() => {
    if (currentCapture) {
      setCaptureProgress(0);
      setCaptureStatus('capturing');
      setDeviceError('');
    }
  }, [currentCapture]);

  const closeDialog = useCallback(() => {
    setCaptureDialog(false);
    setCurrentCapture(null);
    setCaptureStatus('');
    setCaptureProgress(0);
  }, []);

  const hasLeftThumb = Boolean(formik.values.left_thumb_fingerprint);
  const hasRightThumb = Boolean(formik.values.right_thumb_fingerprint);
  const allCaptured = hasLeftThumb && hasRightThumb;

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <FingerprintIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" component="h2">
            Biometric Capture
          </Typography>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Capture left and right thumb fingerprints using the Futronic FS88H device. 
          Both fingerprints are required for citizen registration and fraud prevention.
        </Typography>

        {/* Device Status */}
        <Paper sx={{ p: 2, mb: 3, bgcolor: deviceConnected ? 'success.light' : 'error.light' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <UsbIcon color={deviceConnected ? 'success' : 'error'} />
            <Typography variant="subtitle2">
              Device Status: {deviceConnected ? 'Connected' : 'Disconnected'}
            </Typography>
            {!deviceConnected && (
              <Button
                size="small"
                startIcon={<RefreshIcon />}
                onClick={checkDeviceConnection}
                disabled={loading}
              >
                Retry
              </Button>
            )}
          </Box>
          {deviceError && (
            <Alert severity="error" sx={{ mt: 1 }}>
              {deviceError}
            </Alert>
          )}
        </Paper>

        {/* Fingerprint Capture Grid */}
        <Grid container spacing={3}>
          {/* Left Thumb */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Left Thumb
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <FingerprintIcon 
                  sx={{ 
                    fontSize: 80, 
                    color: hasLeftThumb ? 'success.main' : 'grey.400' 
                  }} 
                />
              </Box>

              {hasLeftThumb ? (
                <Box>
                  <Chip 
                    icon={<CheckIcon />} 
                    label="Captured" 
                    color="success" 
                    sx={{ mb: 2 }}
                  />
                  <br />
                  <Button
                    variant="outlined"
                    onClick={() => startCapture('left')}
                    disabled={!deviceConnected || loading}
                    startIcon={<RefreshIcon />}
                  >
                    Recapture
                  </Button>
                </Box>
              ) : (
                <Button
                  variant="contained"
                  onClick={() => startCapture('left')}
                  disabled={!deviceConnected || loading}
                  startIcon={<FingerprintIcon />}
                  size="large"
                >
                  Capture Left Thumb
                </Button>
              )}
            </Paper>
          </Grid>

          {/* Right Thumb */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Right Thumb
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <FingerprintIcon 
                  sx={{ 
                    fontSize: 80, 
                    color: hasRightThumb ? 'success.main' : 'grey.400' 
                  }} 
                />
              </Box>

              {hasRightThumb ? (
                <Box>
                  <Chip 
                    icon={<CheckIcon />} 
                    label="Captured" 
                    color="success" 
                    sx={{ mb: 2 }}
                  />
                  <br />
                  <Button
                    variant="outlined"
                    onClick={() => startCapture('right')}
                    disabled={!deviceConnected || loading}
                    startIcon={<RefreshIcon />}
                  >
                    Recapture
                  </Button>
                </Box>
              ) : (
                <Button
                  variant="contained"
                  onClick={() => startCapture('right')}
                  disabled={!deviceConnected || loading}
                  startIcon={<FingerprintIcon />}
                  size="large"
                >
                  Capture Right Thumb
                </Button>
              )}
            </Paper>
          </Grid>
        </Grid>

        {/* Progress Summary */}
        {(hasLeftThumb || hasRightThumb) && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Capture Progress: {(hasLeftThumb ? 1 : 0) + (hasRightThumb ? 1 : 0)} of 2 completed
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={((hasLeftThumb ? 1 : 0) + (hasRightThumb ? 1 : 0)) * 50} 
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>
        )}

        {allCaptured && (
          <Alert severity="success" sx={{ mt: 3 }}>
            <Typography variant="subtitle2">
              ✅ All fingerprints captured successfully! You can proceed to the next step.
            </Typography>
          </Alert>
        )}

        {/* Validation Errors */}
        {formik.touched.left_thumb_fingerprint && formik.errors.left_thumb_fingerprint && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {formik.errors.left_thumb_fingerprint}
          </Alert>
        )}
        {formik.touched.right_thumb_fingerprint && formik.errors.right_thumb_fingerprint && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {formik.errors.right_thumb_fingerprint}
          </Alert>
        )}

        {/* Capture Dialog */}
        <Dialog
          open={captureDialog}
          onClose={closeDialog}
          maxWidth="md"
          fullWidth
          disableEscapeKeyDown={captureStatus === 'capturing'}
        >
          <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            Capturing {currentCapture} Thumb Fingerprint
            {captureStatus !== 'capturing' && (
              <IconButton onClick={closeDialog}>
                <CloseIcon />
              </IconButton>
            )}
          </DialogTitle>
          <DialogContent>
            <FingerprintCapture
              thumbType={currentCapture}
              onCaptureComplete={handleCaptureComplete}
              onCaptureError={handleCaptureError}
              onProgressUpdate={setCaptureProgress}
              onStatusUpdate={setCaptureStatus}
              deviceConnected={deviceConnected}
            />
          </DialogContent>
          <DialogActions>
            {captureStatus === 'error' && (
              <Button onClick={retryCapture} startIcon={<RefreshIcon />}>
                Retry
              </Button>
            )}
            {captureStatus !== 'capturing' && (
              <Button onClick={closeDialog}>
                Close
              </Button>
            )}
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default BiometricCaptureStep;
