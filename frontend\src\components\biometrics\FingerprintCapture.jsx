import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Alert,
  CircularProgress,
  LinearProgress,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
} from '@mui/material';
import {
  Fingerprint as FingerprintIcon,
  TouchApp as TouchIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';

const FingerprintCapture = ({
  thumbType,
  onCaptureComplete,
  onCaptureError,
  onProgressUpdate,
  onStatusUpdate,
  deviceConnected
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [capturing, setCapturing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState('');
  const [fingerprintData, setFingerprintData] = useState(null);
  const [quality, setQuality] = useState(0);
  const captureTimeoutRef = useRef(null);
  const progressIntervalRef = useRef(null);

  const steps = [
    'Place finger on sensor',
    'Capturing fingerprint',
    'Processing template',
    'Validation complete'
  ];

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (captureTimeoutRef.current) {
        clearTimeout(captureTimeoutRef.current);
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  // Start capture when component mounts
  useEffect(() => {
    if (deviceConnected && thumbType) {
      startCapture();
    }
  }, [deviceConnected, thumbType]);

  const startCapture = useCallback(async () => {
    try {
      setCapturing(true);
      setError('');
      setActiveStep(0);
      setProgress(0);
      onStatusUpdate('capturing');
      onProgressUpdate(0);

      // Step 1: Wait for finger placement
      setActiveStep(1);
      await simulateFingerPlacement();

      // Step 2: Capture fingerprint
      setActiveStep(2);
      const capturedData = await simulateFingerprintCapture();

      // Step 3: Process template
      setActiveStep(3);
      const processedTemplate = await simulateTemplateProcessing(capturedData);

      // Step 4: Complete
      setActiveStep(4);
      setFingerprintData(processedTemplate);
      onStatusUpdate('complete');
      onCaptureComplete(thumbType, processedTemplate);

    } catch (err) {
      console.error('Fingerprint capture error:', err);
      setError(err.message);
      onStatusUpdate('error');
      onCaptureError(err);
    } finally {
      setCapturing(false);
    }
  }, [thumbType, onCaptureComplete, onCaptureError, onStatusUpdate, onProgressUpdate]);

  const simulateFingerPlacement = useCallback(() => {
    return new Promise((resolve, reject) => {
      // Simulate waiting for finger placement
      let waitTime = 0;
      const maxWaitTime = 10000; // 10 seconds timeout

      const checkInterval = setInterval(() => {
        waitTime += 100;
        const progressPercent = Math.min((waitTime / 3000) * 100, 100);
        setProgress(progressPercent);
        onProgressUpdate(progressPercent);

        if (waitTime >= 3000) {
          clearInterval(checkInterval);
          resolve();
        } else if (waitTime >= maxWaitTime) {
          clearInterval(checkInterval);
          reject(new Error('Timeout waiting for finger placement'));
        }
      }, 100);

      captureTimeoutRef.current = setTimeout(() => {
        clearInterval(checkInterval);
        reject(new Error('Timeout waiting for finger placement'));
      }, maxWaitTime);
    });
  }, [onProgressUpdate]);

  const simulateFingerprintCapture = useCallback(() => {
    return new Promise((resolve, reject) => {
      // Simulate actual fingerprint capture from Futronic FS88H
      let captureProgress = 0;
      
      const captureInterval = setInterval(() => {
        captureProgress += 10;
        setProgress(captureProgress);
        onProgressUpdate(captureProgress);

        if (captureProgress >= 100) {
          clearInterval(captureInterval);
          
          // Simulate random quality score
          const qualityScore = Math.floor(Math.random() * 30) + 70; // 70-100
          setQuality(qualityScore);

          if (qualityScore < 75) {
            reject(new Error(`Fingerprint quality too low (${qualityScore}%). Please clean your finger and try again.`));
          } else {
            // Generate mock fingerprint template data
            const mockTemplate = generateMockFingerprintTemplate(thumbType, qualityScore);
            resolve(mockTemplate);
          }
        }
      }, 200);

      progressIntervalRef.current = captureInterval;
    });
  }, [thumbType, onProgressUpdate]);

  const simulateTemplateProcessing = useCallback((rawData) => {
    return new Promise((resolve) => {
      // Simulate template processing
      setTimeout(() => {
        const processedTemplate = {
          ...rawData,
          processed: true,
          timestamp: new Date().toISOString(),
          deviceId: 'FS88H_001',
          templateVersion: '1.0'
        };
        resolve(processedTemplate);
      }, 1000);
    });
  }, []);

  const generateMockFingerprintTemplate = useCallback((thumbType, quality) => {
    // Generate a mock fingerprint template
    // In real implementation, this would come from the Futronic SDK
    const template = {
      thumbType,
      quality,
      template: btoa(`FINGERPRINT_TEMPLATE_${thumbType.toUpperCase()}_${Date.now()}_${Math.random()}`),
      minutiae: Array.from({ length: Math.floor(Math.random() * 20) + 30 }, (_, i) => ({
        x: Math.floor(Math.random() * 256),
        y: Math.floor(Math.random() * 256),
        angle: Math.floor(Math.random() * 360),
        type: Math.random() > 0.5 ? 'ridge_ending' : 'bifurcation'
      })),
      captureTime: new Date().toISOString(),
      deviceInfo: {
        model: 'Futronic FS88H',
        serialNumber: 'FS88H001',
        firmwareVersion: '1.2.3'
      }
    };
    return JSON.stringify(template);
  }, []);

  const getStepIcon = (stepIndex) => {
    if (stepIndex < activeStep) {
      return <CheckIcon color="success" />;
    } else if (stepIndex === activeStep && capturing) {
      return <CircularProgress size={24} />;
    } else if (stepIndex === 0) {
      return <TouchIcon />;
    } else if (stepIndex === 1 || stepIndex === 2) {
      return <FingerprintIcon />;
    } else {
      return <CheckIcon />;
    }
  };

  const getStepContent = (stepIndex) => {
    switch (stepIndex) {
      case 0:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Please place your {thumbType} thumb firmly on the fingerprint sensor.
              Make sure your finger is clean and dry for best results.
            </Typography>
          </Box>
        );
      case 1:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Capturing fingerprint... Keep your finger steady on the sensor.
            </Typography>
            <LinearProgress variant="determinate" value={progress} sx={{ mt: 1 }} />
          </Box>
        );
      case 2:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Processing fingerprint template and validating quality...
            </Typography>
            {quality > 0 && (
              <Typography variant="body2" color="success.main">
                Quality Score: {quality}%
              </Typography>
            )}
          </Box>
        );
      case 3:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="body2" color="success.main">
              ✅ Fingerprint captured successfully with {quality}% quality!
            </Typography>
          </Box>
        );
      default:
        return null;
    }
  };

  if (!deviceConnected) {
    return (
      <Alert severity="error">
        Fingerprint device is not connected. Please check the device connection and try again.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      {/* Fingerprint Visual */}
      <Paper sx={{ p: 3, textAlign: 'center', mb: 3, bgcolor: 'grey.50' }}>
        <FingerprintIcon 
          sx={{ 
            fontSize: 120, 
            color: activeStep >= 3 ? 'success.main' : 'primary.main',
            mb: 2
          }} 
        />
        <Typography variant="h6" gutterBottom>
          {thumbType.charAt(0).toUpperCase() + thumbType.slice(1)} Thumb Capture
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {capturing ? 'Capturing in progress...' : 'Ready to capture'}
        </Typography>
      </Paper>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Capture Steps */}
      <Stepper activeStep={activeStep} orientation="vertical">
        {steps.map((label, index) => (
          <Step key={label}>
            <StepLabel icon={getStepIcon(index)}>
              {label}
            </StepLabel>
            <StepContent>
              {getStepContent(index)}
            </StepContent>
          </Step>
        ))}
      </Stepper>

      {/* Retry Button */}
      {error && (
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Button
            variant="contained"
            onClick={startCapture}
            startIcon={<FingerprintIcon />}
          >
            Retry Capture
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default FingerprintCapture;
